package com.seer.trick.fleet.service

import com.seer.trick.fleet.traffic.distributed.helper.AngleHelper
import com.seer.trick.fleet.traffic.distributed.lock.base.RobotContainerSpaceLock
import org.slf4j.LoggerFactory

/**
 * 窄边通行服务
 * 
 * 窄边通行是控制容器以窄边向前方向进入路径的配置，需配合换向点使用。
 * 从distributed系统移植的窄边通行实现，用于Venus路径规划系统。
 */
object NarrowSideAhead {
    
    private val logger = LoggerFactory.getLogger(NarrowSideAhead::class.java)
    
    /**
     * 查询容器模型的窄边方向
     * 
     * @param sceneId 场景ID
     * @param containerType 容器类型
     * @return 窄边方向角度，0表示无窄边要求，90度表示需要窄边通行
     */
    fun queryContainerModelNarrowDir(sceneId: String, containerType: String): Int {
        return try {
            RobotContainerSpaceLock.queryContainerModelNarrowDir(sceneId, containerType)
        } catch (e: Exception) {
            logger.warn("Failed to query narrow dir for container type: $containerType in scene: $sceneId", e)
            0
        }
    }
    
    /**
     * 根据容器尺寸计算窄边方向
     * 
     * @param outerLength 容器外部长度
     * @param outerWidth 容器外部宽度
     * @return 窄边方向角度：长>宽时返回0，宽>=长时返回90度
     */
    fun calculateNarrowDir(outerLength: Double, outerWidth: Double): Int {
        return if (outerLength > outerWidth) 0 else AngleHelper.RIGHT_ANGLE
    }
    
    /**
     * 计算窄边通行时的容器朝向
     * 
     * @param enterDir 进入方向角度
     * @param narrowDir 窄边方向偏移角度
     * @return 窄边通行时容器可能的朝向列表
     */
    fun calculateNarrowPathLoadingHeadings(enterDir: Int, narrowDir: Int): List<Int> {
        val loadingHeadings = mutableListOf<Int>()
        loadingHeadings.add(AngleHelper.processAngle(enterDir + narrowDir))
        loadingHeadings.add(AngleHelper.processAngle(enterDir + AngleHelper.DOWN_ANGLE + narrowDir))
        return loadingHeadings
    }
    
    /**
     * 检查是否需要窄边通行
     * 
     * @param sceneId 场景ID
     * @param containerType 容器类型
     * @return true如果需要窄边通行，false否则
     */
    fun requiresNarrowSideAhead(sceneId: String, containerType: String?): Boolean {
        if (containerType.isNullOrBlank()) return false
        return queryContainerModelNarrowDir(sceneId, containerType) != 0
    }
    
    /**
     * 根据窄边方向调整容器朝向
     * 
     * @param originalTheta 原始容器朝向
     * @param narrowDir 窄边方向偏移角度
     * @return 调整后的容器朝向
     */
    fun adjustLoadThetaForNarrowPath(originalTheta: Double, narrowDir: Int): Double {
        if (narrowDir == 0) return originalTheta
        
        // 将窄边方向转换为弧度并应用
        val narrowDirRadians = Math.toRadians(narrowDir.toDouble())
        return (originalTheta + narrowDirRadians) % (2 * Math.PI)
    }
    
    /**
     * 判断给定的路径是否需要窄边通行
     * 
     * @param path 地图路径
     * @param options 路径规划选项
     * @param robotInfo 机器人信息
     * @return true如果需要窄边通行
     */
    fun isNarrowPathRequired(path: com.seer.trick.fleet.domain.MapPath?, options: com.seer.trick.fleet.traffic.venus.PathFindingOption, robotInfo: com.seer.trick.fleet.traffic.venus.RobotInfo): Boolean {
        return path != null && 
               path.containerShortSideAhead && 
               options.enableNarrowSideAhead && 
               options.sceneId != null && 
               robotInfo.narrowDir != 0
    }
    
    /**
     * 计算窄边通行时的容器朝向
     * 类似于distributed系统中的findLoadingHeadings逻辑
     * 
     * @param robotEnterTheta 机器人进入路径的角度（弧度）
     * @param narrowDir 窄边方向偏移角度
     * @return 窄边通行时的容器朝向（弧度）
     */
    fun calculateNarrowPathLoadTheta(robotEnterTheta: Double, narrowDir: Int): Double {
        val enterDirDegrees = Math.toDegrees(robotEnterTheta).toInt()
        val narrowLoadingHeadings = calculateNarrowPathLoadingHeadings(enterDirDegrees, narrowDir)
        
        // 使用第一个朝向作为默认选择，保持与distributed系统一致
        val bestNarrowHeading = narrowLoadingHeadings.first()
        return Math.toRadians(bestNarrowHeading.toDouble())
    }
}