package com.seer.trick.fleet.traffic.venus

import com.seer.trick.base.concurrent.PollingJobManager
import com.seer.trick.fleet.FleetLog
import com.seer.trick.fleet.FleetLogger
import com.seer.trick.fleet.FleetLoggerSubject
import com.seer.trick.fleet.domain.*
import com.seer.trick.fleet.order.ParkingService
import com.seer.trick.fleet.service.*
import com.seer.trick.fleet.traffic.TrafficService
import com.seer.trick.fleet.traffic.TrafficTaskStatus
import java.util.*
import java.util.concurrent.ConcurrentHashMap
import kotlin.math.max
import kotlin.math.min

/**
 * 基于 MAPF 的交管实现
 */
class VenusTrafficService(override val sr: SceneRuntime) : TrafficService() {

  private val log = FleetLog(FleetLoggerSubject.TP2)

  /**
   * 计算 ADG 时是否考虑到自己的依赖
   */
  private val adgSelfDep = false

  /**
   * 最多并行执行的路径段数
   */
  private val maxExecuting = 4

  /**
   * 为了保证下发的路径足够长（>4m），在计算待下发节点数量时，会综合考虑距离。
   */
  private val minReleaseDistance = 6.0 // meter

  /**
   * 机器人执行情况监控
   */
  private val robotExecutionContexts: MutableMap<String, VenusRobotExecutionContext> = mutableMapOf()

  /**
   * 分区域 ADG
   */
  private val adgAreas: MutableMap<Int, AdgArea> = ConcurrentHashMap()

  /**
   * 主循环上次抛异常了
   */
  @Volatile
  private var mainLoopExceptionLast = false

  override fun init() {
    super.init()

    VenusLogService.start()

    PollingJobManager.submit(
      threadName = getThreadName(sr),
      remark = "Traffic manager TP2 for scene " + sr.basic.name,
      interval = { 1000L },
      logger = logger,
      workerMaxTime = 5 * 1000,
      stopCondition = { sr.status == SceneStatus.Disposed || disposed },
      exceptionContinue = true,
      tags = setOf(sr.tag),
      worker = ::mainLoop,
    )
  }

  /**
   * Venus 目前不需要什么复杂处理
   */
  override fun robotEnterTraffic(rr: RobotRuntime) {
    super.robotEnterTraffic(rr)
    rr.trafficReady = true
  }

  /**
   * Venus 目前不需要什么复杂处理
   */
  override fun robotExitTraffic(rr: RobotRuntime) {
    super.robotExitTraffic(rr)
    rr.trafficReady = false
  }

  /**
   * 要加同步防止规划时改变
   * 注意：目前会跳着报，不一定连续报
   * TODO 锁的合理性
   */
  override fun afterMoveActionDone(rr: RobotRuntime, move: MoveActionReq) = sr.withOrderLock {
    super.afterMoveActionDone(rr, move)

    // 当前执行步骤已被取消
    val sec = rr.executingStep ?: return@withOrderLock
    if (sec.withdrawn) return@withOrderLock

    val ec = robotExecutionContexts[rr.robotName] ?: return@withOrderLock
    // TODO 抛异常改成让机器人故障
    val path = ec.path

    log.info(
      sr,
      rr,
      sec.or,
      "MoveActionDone. toPoint=${move.toPointName}, " +
        "moveIndex=${move.index}, actualIndex=${move.index - ec.pathIndexOffset}, " +
        "done=${ec.donePathIndex}, commit=${ec.committedPathIndex}, release=${ec.releasedPathIndex}, " +
        "offset=${ec.pathIndexOffset}, pathSize=${path.size}",
    )

    // < 已完成，是 BUG
    if (move.index.toInt() <= ec.donePathIndex && !ec.goalEdgeReleased) {
      log.error(
        sr,
        rr,
        sec.or,
        "PathNodeDoneBadDoneIndex. toPoint=${move.toPointName}, " +
          "moveIndex=${move.index}, actualIndex=${move.index - ec.pathIndexOffset}, " +
          "done=${ec.donePathIndex}, commit=${ec.committedPathIndex}, release=${ec.releasedPathIndex}, " +
          "offset=${ec.pathIndexOffset}, pathSize=${path.size}",
      )

      // TODO 标记机器人故障
      return@withOrderLock
    }

    // > 下发，是 BUG
    if (move.index.toInt() > ec.releasedPathIndex) {
      log.error(
        sr,
        rr,
        sec.or,
        "PathNodeDoneButDoneGtReleased. toPoint=${move.toPointName}, " +
          "moveIndex=${move.index}, actualIndex=${move.index - ec.pathIndexOffset}, " +
          "done=${ec.donePathIndex}, commit=${ec.committedPathIndex}, release=${ec.releasedPathIndex}, " +
          "offset=${ec.pathIndexOffset}, pathSize=${path.size}",
      )
      // TODO 标记机器人故障
      return@withOrderLock
    }

    // 当前完成 N，此次上报可能是 N+M，M>=1
    val doneIndexes = (ec.donePathIndex + 1)..move.index.toInt()
    ec.donePathIndex = move.index.toInt()

    val area = adgAreas[ec.areaId] ?: return@withOrderLock // 不会为空
    for (index in doneIndexes) {
      val adgKey = toAdgNodeKey(rr.robotName, index)
      area.finishedAdgNodes += adgKey
    }
  }

  /**
   * MoveAction 失败 —— 此方法目标不会被调用
   */
  override fun afterMoveActionFailed(rr: RobotRuntime, orderId: String?) {
    robotExecutionContexts.remove(rr.robotName)
  }

  override fun afterTrafficTaskDone(rr: RobotRuntime): Boolean {
    robotExecutionContexts.remove(rr.robotName)
    return true
  }

  override fun afterTrafficTaskCancelled(rr: RobotRuntime) {
    robotExecutionContexts.remove(rr.robotName)
  }

  override fun resetByRobot(rr: RobotRuntime) {
    log.error(sr, rr, null, "ResetByRobot")
    robotExecutionContexts.remove(rr.robotName)
  }

  override fun resetAll() {
    log.error(sr, null, null, "ResetByRobot")
    robotExecutionContexts.clear()
  }

  override fun showTrafficResourceMessage(robotName: String): RobotShowTrafficMessage? {
    val ec = robotExecutionContexts[robotName] ?: return null
    val path = ec.path

    val spaceResources = mutableListOf<SpaceResource>()
    for (i in ec.donePathIndex + 1..ec.releasedPathIndex) {
      if (i - ec.pathIndexOffset >= path.size) continue
      val s = path[i - ec.pathIndexOffset]
      if (s.shapes != null) {
        for (shape in s.shapes.polys) {
          spaceResources += SpaceResource(SpaceResourceType.Polygon, points = shape.points)
        }
      }
    }

    val traveledPointNames = mutableListOf<String>()
    val untraveledPointNames = mutableListOf<String>()
    val doneIndex = ec.donePathIndex - ec.pathIndexOffset
    for ((i, state) in path.withIndex()) {
      val p = state.toPosition.pointName ?: continue
      // 跟 TP1 保持一致，刚完成的点也在 untraveled
      if (i < doneIndex) {
        traveledPointNames += p
      } else {
        untraveledPointNames += p
      }
    }

    return RobotShowTrafficMessage(
      pathResource = PathResource(traveledPointNames, untraveledPointNames),
      spaceResources = spaceResources,
    )
  }

  override fun showTrafficResourceMessage(): Map<String, RobotShowTrafficMessage> {
    val msgMap = mutableMapOf<String, RobotShowTrafficMessage>()
    // 返回机器人当前占用的空间资源
    for (ec in robotExecutionContexts.values) {
      val msg = showTrafficResourceMessage(ec.robotName)
      if (msg != null) msgMap[ec.robotName] = msg
    }
    return msgMap
  }

  /**
   * 主循环
   */
  private fun mainLoop() {
    resolve()

    for (ec in robotExecutionContexts.values) {
      commit(ec)
      release(ec)
    }
  }

  /**
   * 求解
   */
  private fun resolve() {
    // 获得分区域求解结果
    val results = PlanService.plan(sr, robotExecutionContexts)

    for ((areaId, result) in results) {
      val adgArea = adgAreas.getOrPut(areaId) { AdgArea(areaId) }
      if (result.ok) {
        // TODO 为什么要加锁
        sr.withOrderLock {
          // TODO 应不应该全 clear
          adgArea.finishedAdgNodes.clear()

          for (sol in result.solutions.values) {
            startRobotExecution(sol, adgArea)
          }

          // TODO 构造 ADG 应该不耗时吧
          // 最后构造依赖并记录耗时
          val t0 = System.currentTimeMillis()
          // 如果构建 adg 失败，说明可能被取消了
          buildAdg(result, adgArea)
          val elapsed = System.currentTimeMillis() - t0
          FleetLogger.info(
            module = logModuleTraffic,
            subject = "BuildAdgElapsed",
            sr = sr,
            robotName = null,
            msg = mapOf(
              "elapsedMs" to elapsed,
              "adgNodes" to adgArea.adgNodes.size,
            ),
          )
        }
      } else {
        logger.error("Plan failed: ${result.reason}")
        // 规划失败，暂停规划
        // TODO 先告警
      }
    }
  }

  /**
   * 有让路的，先去让路
   */
  private fun doGiveWay(result: PlanResult) {
    for ((robotName, goal) in result.giveWayGoals) {
      val rr = sr.mustGetRobot(robotName)
      if (rr.autoOrder != null || rr.executingStep != null) {
        // 极端情况下会让一个刚接单的机器人避让停靠
        FleetLogger.info(
          module = logModuleTraffic,
          subject = "GiveWayToParkingButOld",
          sr = sr,
          robotName = rr.robotName,
          msg = mapOf("goal" to goal, "oldAutoOrder" to rr.autoOrder),
        )
      } else {
        FleetLogger.info(
          module = logModuleTraffic,
          subject = "ToGiveWay",
          sr = sr,
          robotName = rr.robotName,
          msg = mapOf("goal" to goal),
        )
        // 利用停靠单实现避让
        ParkingService.doParkingOrder(sr, rr, goal)
      }
    }
  }

  /**
   * 将求解结果给机器人
   */
  private fun startRobotExecution(sol: RobotSolution, adgArea: AdgArea): Boolean {
    val rr = sr.mustGetRobot(sol.robotName)
    val tt = rr.pendingTrafficTask ?: return false
    if (rr.pendingTrafficTask?.status != TrafficTaskStatus.TrafficAccepted) return false
    if (!sol.ok) return false

    FleetLogger.info(
      module = logModuleTraffic,
      subject = "RobotPlanSolutionOk",
      sr = sr,
      robotName = sol.robotName,
      msg = mapOf("solution" to sol),
    )

    // TODO 结合机器人之前的执行上下文，构造新的
    val ec = robotExecutionContexts.compute(sol.robotName) { _, old ->
      if (old == null || old.path.isEmpty()) {
        // TODO 除了已下发的，没有新路径的的意思？
        if (sol.path.any { it.released }) {
          FleetLogger.info(
            module = logModuleTraffic,
            subject = "releasedAllFinished",
            sr = sr,
            robotName = sol.robotName,
            msg = mapOf("solution" to sol),
          )
          return@compute null
        }
        // 新路径
        VenusRobotExecutionContext(
          robotName = sol.robotName,
          areaId = adgArea.areaId,
          trafficTaskId = tt.id,
          trafficTask = tt,
          path = sol.path,
        )
      } else {
        // 保留进度信息，替换为新路径
        val doneState = old.path[old.donePathIndex - old.pathIndexOffset]
        // 预留路径中一个点位可能会出现多次，为了避免异常，通过 pathIndex 来进行定位
        val cutIndex = sol.path.indexOfFirst {
          it.isSameLocation(doneState) &&
            it.pathIndex == doneState.pathIndex
        }
        // cutIndex == 0 表示已在首位；<0 表示未找到
        val newPath = if (cutIndex <= 0) {
          sol.path
        } else {
          sol.path.subList(cutIndex, sol.path.size)
        }
        val newEc = VenusRobotExecutionContext(
          robotName = old.robotName,
          areaId = adgArea.areaId,
          trafficTaskId = old.trafficTaskId,
          trafficTask = old.trafficTask,
          path = newPath,
        )
        sol.path = newPath
        newEc.donePathIndex = old.donePathIndex
        newEc.releasedPathIndex = old.releasedPathIndex
        newEc.committedPathIndex = old.committedPathIndex
        newEc.pathIndexOffset = old.pathIndexOffset
        newEc.goalEdgeReleasedIndex = old.goalEdgeReleasedIndex
        newEc.goalEdgeReleased = old.goalEdgeReleased
        newEc
      }
    } ?: return false

    // 规划成功再修改路径索引偏移！
    ec.pathIndexOffset = ec.donePathIndex
    ec.committedPathIndex = ec.releasedPathIndex

    FleetLogger.info(
      module = logModuleTraffic,
      subject = "editPathIndexOffset",
      sr = sr,
      robotName = sol.robotName,
      msg = mapOf(
        "pathIndexOffset" to ec.pathIndexOffset,
        "donePathIndex" to ec.donePathIndex,
        "releasedPathIndex" to ec.releasedPathIndex,
        "commitIndex" to ec.committedPathIndex,
        "pathSize" to ec.path.size,
        "path" to ec.path,
      ),
    )
    // 前面的都让他完成
    for (i in 0..(ec.donePathIndex)) {
      adgArea.finishedAdgNodes += toAdgNodeKey(sol.robotName, ec.donePathIndex)
    }
    return true
  }

  /**
   * 标记已满足的路径待下发（commited index）
   */
  private fun commit(ec: VenusRobotExecutionContext) {
    val path = ec.path
    val oldCommittedPathIndex = ec.committedPathIndex
    var commitPaths = ""

    val adgArea = adgAreas[ec.areaId] ?: return // 不会为空

    while (ec.committedPathIndex - ec.pathIndexOffset < path.size - 1) {
      val nextIndex = ec.committedPathIndex + 1
      if ((nextIndex - ec.pathIndexOffset) > 17) return
      // val s1 = path[rt.committedPathIndex - rt.pathIndexOffset]
      val s2 = path[nextIndex - ec.pathIndexOffset]
      // 原地停车并不下发
      if (s2.toPosition.pointName == ec.trafficTask.target.pointName &&
        s2.type == StateType.Goal &&
        ec.trafficTask.target.pointName != ec.trafficTask.source.pointName
      ) {
        return
      }
      // 预留路径并不真正下发
      if (s2.reserve) return
      // if (s2.type == StateType.Goal) throw BzError("errCodeErr", "不能下发 Goal 节点")
      val adgKey = toAdgNodeKey(ec.robotName, nextIndex)

      val dependents = adgArea.adgNodes[adgKey]
      var dependentsAllPass = true
      var blockingDeps: MutableList<String>? = null
      val allDeps: MutableList<String> = mutableListOf()
      if (dependents != null) {
        // todo 资源申请？，目前原地停留是没有资源的
        for (d in dependents.topology + dependents.spatial) {
          allDeps.add(d)
          if (!adgArea.finishedAdgNodes.contains(d)) {
            if (blockingDeps == null) blockingDeps = mutableListOf()
            blockingDeps.add(d)
            dependentsAllPass = false
          }
        }
      }

      // 详细日志：输出依赖及阻塞情况
      FleetLogger.debug(
        module = logModuleTraffic,
        subject = if (dependentsAllPass) "CommitDeps" else "CommitBlocked",
        sr = sr,
        robotName = ec.robotName,
        msg = mapOf(
          "nextIndex" to nextIndex,
          "adgKey" to adgKey,
          "allDeps" to allDeps,
          "blockingDeps" to (blockingDeps ?: emptyList()),
          "finishedNodes.size" to adgArea.finishedAdgNodes.size,
          "pathIndexOffset" to ec.pathIndexOffset,
          "committedPathIndex" to ec.committedPathIndex,
          "state" to s2,
          "path" to ec.path,
        ),
      )

      if (dependentsAllPass) {
        // 标记待下发
        ++ec.committedPathIndex
        commitPaths += "[$nextIndex]${s2.byPathKey}, "
      } else {
        break
      }
    }
    if (ec.committedPathIndex > oldCommittedPathIndex) {
      FleetLogger.info(
        module = logModuleTraffic,
        subject = "CommitPathNode",
        sr = sr,
        robotName = ec.robotName,
        msg = mapOf(
          "commitRange" to "$oldCommittedPathIndex -> ${ec.committedPathIndex}",
          "pathIndexOffset" to ec.pathIndexOffset,
          "actual" to "${oldCommittedPathIndex - ec.pathIndexOffset} -> ${ec.committedPathIndex - ec.pathIndexOffset}",
          "path" to commitPaths,
          "offset" to ec.pathIndexOffset,
          "done" to ec.donePathIndex,
          "release" to ec.releasedPathIndex,
          "commitIndex" to ec.committedPathIndex,
          "pathSize" to path.size,
        ),
      )
    }
  }

  /**
   * 下发可下发的节点给机器人
   */
  private fun release(ec: VenusRobotExecutionContext) {
    val path = ec.path

    val adgArea = adgAreas[ec.areaId] ?: return // 不会为空

    // 如果到终点的路径已经下发，判断是否到达终点，可能到达终点在做任务，导致任务没完成
    if (ec.goalEdgeReleased) {
      val rr = sr.mustGetRobot(ec.robotName)
      val stand = rr.selfReport?.stand
      if (stand?.pointName == ec.trafficTask.target.pointName) {
        if (ec.donePathIndex + 1 == ec.goalEdgeReleasedIndex) {
          ec.donePathIndex = ec.goalEdgeReleasedIndex
          val adgKey = toAdgNodeKey(rr.robotName, ec.donePathIndex)
          adgArea.finishedAdgNodes += adgKey
        }
      }
    }

    if (ec.committedPathIndex > ec.releasedPathIndex) {
      // 已下发但未完成的距离
      var currentDist = 0.0
      for (i in ec.donePathIndex + 1..ec.releasedPathIndex) {
        val idx = i - ec.pathIndexOffset
        if (idx <= 0) continue
        val sPrev = path[idx - 1]
        val sCurr = path[idx]
        currentDist +=
          GeoHelper.euclideanDistance(sPrev.toPosition.x, sPrev.toPosition.y, sCurr.toPosition.x, sCurr.toPosition.y)
      }

      // --- 计算基于距离和基于段数的两个候选 releasedIndex ---
      // 基于距离：直到累计距离 >= minReleaseDistance
      var distIdx = ec.releasedPathIndex
      var accDist = currentDist
      while (distIdx < ec.committedPathIndex && accDist < minReleaseDistance) {
        val idxInPath = distIdx + 1 - ec.pathIndexOffset
        val sPrev = path[idxInPath - 1]
        val sNext = path[idxInPath]
        accDist +=
          GeoHelper.euclideanDistance(sPrev.toPosition.x, sPrev.toPosition.y, sNext.toPosition.x, sNext.toPosition.y)
        ++distIdx
      }

      // 基于段数：保证执行段数 <= maxExecuting
      val currentCount = ec.releasedPathIndex - ec.donePathIndex
      val countIdx = min(ec.releasedPathIndex + (maxExecuting - currentCount), ec.committedPathIndex)

      // 取两者较大值 -> 下发更多的节点
      val newReleasedIndex = max(distIdx, countIdx)

      if (newReleasedIndex > ec.releasedPathIndex) {
        val moves: MutableList<MoveActionRuntime> = ArrayList(newReleasedIndex - ec.releasedPathIndex)
        for (i in ec.releasedPathIndex + 1..newReleasedIndex) {
          val s1 = path[i - 1 - ec.pathIndexOffset]
          val s2 = path[i - ec.pathIndexOffset]
          // if (s2.type == StateType.Goal) throw BzError("errCodeErr", "不能下发 Goal 节点")
          val toPointName = s2.toPosition.pointName ?: s2.toPosition.pathEndPointName!!
          val fromPointName = (
            s1.toPosition.pointName
              ?: // 机器人在路线上，此时最好不要与 toPointName 一样，因为如果一样下层会认为是原地任务,但实际并不是
              if (s1.toPosition.pathStartPointName != toPointName) {
                s1.toPosition.pathStartPointName
              } else {
                s1.toPosition.pathEndPointName
              }
            )!!

          moves += MoveActionRuntime(
            ec.trafficTaskId,
            MoveActionReq(
              toPointName = toPointName,
              fromPointName = fromPointName,
              index = i.toLong(),
              robotEnterTheta = s2.robotEnterTheta,
              robotExitTheta = s2.robotExitTheta,
              loadEnterTheta = s2.loadEnterTheta, // 添加容器进入角度传递
              loadExitTheta = s2.loadExitTheta,   // 添加容器退出角度传递
              finalAction = s2.toPosition.pointName == ec.trafficTask.target.pointName, // 本次规划最后一段
              rotationDirection = s2.rotateDirection,
              orderId = ec.trafficTask.orderId,
              stepIndex = ec.trafficTask.stepIndex,
              stepId = ec.trafficTask.stepId,
              trafficMethod = TrafficMethod.Venus,
            ),
          )
        }

        FleetLogger.info(
          module = logModuleTraffic,
          subject = "ReleaseMoves",
          sr = sr,
          robotName = ec.robotName,
          msg = mapOf(
            "releaseIndex" to "${ec.releasedPathIndex + 1} -> $newReleasedIndex",
            "currentDist" to currentDist,
            "distTargetIdx" to distIdx,
            "countTargetIdx" to countIdx,
            "selected" to newReleasedIndex,
            "accumulatedDist" to accDist,
            "moves" to moves,
          ),
        )

        val rr = sr.mustGetRobot(ec.robotName)

        val finalAction = moves.firstOrNull { it.req.finalAction }
        if (finalAction != null) {
          ec.goalEdgeReleasedIndex = finalAction.req.index.toInt()
          ec.goalEdgeReleased = true
        }
        RobotMoveService.appendActions(rr, ec.trafficTask, moves)

        ec.releasedPathIndex = newReleasedIndex
      }
    }
  }

  // 机器人 1 要从 A 到 C，然后从 C 到 D；到达 C 的时刻是 t。
  // 机器人 2 要从 B 到 C，到达 C 的时刻是 t + M，即比机器人 1 晚。
  // 那么一定要机器人 1 先到 D（即确保已离开 C），机器人 2 才能开始从 B 到 C 的移动，比较保险。
  // 否则机器人 1 一边离开 C，机器人 2 同时去 C，如果速度不同步，2 可能撞 1。

  /**
   * 构建 ADG
   * ADG 用于确保机器人执行路径时不会发生冲突，包含两种类型的依赖：
   * 1. 拓扑依赖（topology）：表示路径顺序依赖. but 似乎实际上没用，pathEndPointName 一般都是 null
   * 2. 空间依赖（spatial）：表示空间资源占用冲突
   */
  private fun buildAdg(highResult: PlanResult, adgArea: AdgArea): Boolean {
    // nodes 存储所有节点及其依赖关系
    // key: "机器人名#路径索引"，value: 该节点的依赖关系
    val nodes = mutableMapOf<String, AdgNodeDependencies>()
    if (highResult.ok) {
      // 1. 构建自依赖（如果启用）
      if (adgSelfDep) {
        for (rr in highResult.solutions.values) {
          val path = rr.path
          val ec = robotExecutionContexts[rr.robotName] ?: continue
          // 为每个机器人构建路径段的拓扑依赖：后一段依赖前一段
          for (i in 0 until path.size - 1) {
            val n1key = toAdgNodeKey(rr.robotName, i + ec.pathIndexOffset) // 前一段
            val n2key = toAdgNodeKey(rr.robotName, i + 1 + ec.pathIndexOffset) // 后一段
            nodes.getOrPut(n2key) { AdgNodeDependencies() }.topology.add(n1key)
          }
        }
      }

      // 2. 构建跨机器人依赖
      for (sol1 in highResult.solutions.values) {
        val path1 = sol1.path
        val ec1 = robotExecutionContexts[sol1.robotName] ?: continue
        val rr1 = sr.mustGetRobot(sol1.robotName)

        for (i in path1.indices) {
          val s1 = path1[i]
//          if (s1.reserve) continue
          // 跳过等待和目标状态，因为它们不需要依赖其他机器人
          if (s1.type == StateType.Wait || s1.type == StateType.Goal) continue

          // 对于每个其他机器人
          for (sol2 in highResult.solutions.values) {
            if (sol1.robotName == sol2.robotName) continue // 跳过自己

            val path2 = sol2.path
            val ec2 = robotExecutionContexts[sol2.robotName] ?: continue
            val rr2 = sr.mustGetRobot(sol2.robotName)

            for (j in path2.indices) {
              val s2 = path2[j]
//              if (s2.reserve) continue
              if (s2.type == StateType.Goal) continue // 跳过目标状态

              // 构造节点 key
              val n1key = toAdgNodeKey(rr1.robotName, i + ec1.pathIndexOffset)
              val n2key = toAdgNodeKey(rr2.robotName, j + ec2.pathIndexOffset)

              // 2.1 检查拓扑依赖：s1 要去往 s2 的起点
              if (s1.toPosition.pathEndPointName != null &&
                s1.toPosition.pathEndPointName == s2.toPosition.pathStartPointName &&
                s1.timeStart <= s2.timeEnd
              ) {
                // s1 要去往 s2 的起点，且 s1.timeStart（到达这个状态的最早时间）
                // <= s2.timeEnd（离开这个状态的最晚时间）
                // 这意味着 s1 必须等待 s2 完成后才能开始
                nodes.getOrPut(n1key) { AdgNodeDependencies() }.topology.add(n2key)
              }
              // 2.2 检查空间依赖：
              else if (s1.spatialIntersecting(s2) && (s1.timeStart > s2.timeEnd)) {
                // 当两个状态在空间上重叠时，必须确保时间顺序：
                // s1.timeStart > s2.timeEnd，即 s2 先完成，s1 后开始
                FleetLogger.info(
                  module = logModuleTraffic,
                  subject = "ADGSpaceIntersect",
                  sr = sr,
                  robotName = rr1.robotName,
                  msg = mapOf(
                    "n1key" to n1key,
                    "n2key" to n2key,
                    "s1" to s1,
                    "s2" to s2,
                  ),
                )
                nodes.getOrPut(n1key) { AdgNodeDependencies() }.spatial.add(n2key)
                if (s2 == path2.last()) {
                  // 对于最后一个状态，并不是该状态 done 就可以的，实际上得等其离开，但由于最后一个状态，路径中没有离开的状态，所以主动添加一个
                  nodes.getOrPut(n1key) { AdgNodeDependencies() }.spatial.add("end $n2key")
                }
              } else if (s1.toPosition.pointName == s2.toPosition.pointName && (s1.timeStart > s2.timeEnd)) {
                FleetLogger.error(
                  module = logModuleTraffic,
                  subject = "ADGSpaceIntersectError",
                  sr = sr,
                  robotName = rr1.robotName + "-" + rr2.robotName,
                  msg = mapOf(
                    "name" to s1.toPosition.pointName,
                    "n1key" to n1key,
                    "n2key" to n2key,
                    "s1" to s1,
                    "s2" to s2,
                    "s1shape" to s1.shapes,
                    "s2shape" to s1.shapes,
                  ),
                )
              }
            }
          }
        }
      }

      // 3. 对于没有执行上下文(ec==null)的机器人，以其当前位置构建一个静态节点，
      // 所有与之空间冲突的路径段都必须依赖该静态节点（无视时间）。
      for (solStatic in highResult.solutions.values) {
        if (robotExecutionContexts.containsKey(solStatic.robotName)) continue // 仍有执行上下文
        FleetLogger.info(
          module = logModuleTraffic,
          subject = "ADGStatic",
          sr = sr,
          robotName = solStatic.robotName,
          msg = mapOf(
            "robotName" to solStatic.robotName,
            "path" to solStatic.path,
          ),
        )
        val rr = sr.mustGetRobot(solStatic.robotName)
        val cm = RobotService.getCollisionModel(rr)
        val staticShapes = mutableListOf<Polygon>()
        staticShapes += ResConflictManager.buildVertexSpace(
          cm,
          rr.selfReport!!.stand!!.x,
          rr.selfReport!!.stand!!.y,
          rr.selfReport!!.stand!!.theta,
        )

        // 若能定位到路径中的节点，再加入前后邻接节点形状
        val standPtName = rr.selfReport!!.stand!!.pointName
        if (standPtName != null) {
          val pathStatic = solStatic.path
          val idxOnPath = pathStatic.indexOfFirst { it.toPosition.pointName == standPtName }
          if (idxOnPath >= 0) {
            var startIdx = (idxOnPath - 1).coerceAtLeast(0)
            var endIdx = (idxOnPath + 5).coerceAtMost(pathStatic.size) // exclusive

            // idx+4 若为预留通道，则后续全纳入
            val idxPlus4 = idxOnPath + 4
            if (idxPlus4 in pathStatic.indices && pathStatic[idxPlus4].reserve) {
              endIdx = pathStatic.size
            }

            for (i in startIdx until endIdx) {
              pathStatic[i].shapes?.polys?.takeIf { it.isNotEmpty() }?.let(staticShapes::addAll)
            }
          }
        } else {
          // 无法定位到节点，机器人在路径上：整条路径都纳入
          for (st in solStatic.path) {
            st.shapes?.polys?.takeIf { it.isNotEmpty() }?.let(staticShapes::addAll)
          }
        }

        val staticKey = toAdgNodeKey(rr.robotName, -1) // 用 -1 表示静态节点
        nodes.getOrPut(staticKey) { AdgNodeDependencies() } // 静态节点自身不依赖任何人

        // 遍历其它机器人的路径段，若与静态机器人位置发生空间冲突，则依赖静态节点
        for (solOther in highResult.solutions.values) {
          if (solOther.robotName == solStatic.robotName) continue
          val ec = robotExecutionContexts[solOther.robotName] ?: continue
          val pathOther = solOther.path

          for (idx in pathOther.indices) {
            val s = pathOther[idx]
            if (s.type == StateType.Goal) continue

            if ((
                rr.selfReport!!.stand!!.pointName != null &&
                  rr.selfReport!!.stand!!.pointName == s.toPosition.pointName
                ) ||
              s.spatialIntersecting(staticShapes)
            ) {
              val nOther = toAdgNodeKey(solOther.robotName, idx + ec.pathIndexOffset)
              nodes.getOrPut(nOther) { AdgNodeDependencies() }.spatial.add(staticKey)
            }
          }
        }
      }
    }

    adgArea.adgNodes = nodes
    // 构造完成后输出整体 ADG 信息，便于排查依赖问题
    val topoEdgesCnt = nodes.values.sumOf { it.topology.size }
    val spatialEdgesCnt = nodes.values.sumOf { it.spatial.size }

    FleetLogger.info(
      module = logModuleTraffic,
      subject = "ADGBuilt",
      sr = sr,
      robotName = null,
      msg = mapOf(
        "nodesCnt" to nodes.size,
        "topologyEdgesCnt" to topoEdgesCnt,
        "spatialEdgesCnt" to spatialEdgesCnt,
        // 为避免日志过大，仅打印前 100 个节点依赖，更多依赖可通过 debugData() 拉取
        "sampleNodes" to nodes.entries.take(100).associate { it.key to it.value },
      ),
    )
    return true
  }

  // ADG 的一个顶点的唯一键。
  private fun toAdgNodeKey(r: String, pathIndex: Int) = "$r#$pathIndex"

  override fun debugData(): Any = mapOf(
    "timestamp" to Date(),
    "adgAreas" to adgAreas,
  )

  override fun robotDebugData(robotName: String): VenusRobotExecutionContext? = robotExecutionContexts[robotName]

  /**
   * 获得最后一次求解产生的所有节点
   */
  fun getLastHighNodes(): List<HighNode>? = null // TODO

  companion object {
    fun getThreadName(sr: SceneRuntime) = "TP2-${sr.no}"
  }
}